package api

import (
	"server/api/authority"
	"server/api/base"
	"server/api/business"
	"server/api/configuration"
	"server/api/file_m"
	"server/api/sys_tool"

	//"server/map/fileM"
	"server/api/monitor"
	//"server/map/sysTool"
)

type Group struct {
	Authority     authority.ApiGroup
	Base          base.ApiGroup
	FileM         file_m.ApiGroup
	Monitor       monitor.ApiGroup
	SysTool       sys_tool.ApiGroup
	Configuration configuration.ApiGroup
	Business      business.ApiGroup
}

var GroupApp = new(Group)

package configuration

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"server/global"
	commonReq "server/model/common/request"
	commonRes "server/model/common/response"
	"server/model/configuration"
	configurationReq "server/model/configuration/request"
)

type PackingApi struct {
}

// AddPacking
// @Tags      PackingApi
// @Summary   添加包装方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configuration.PackingModal true "请求参数"
// @Success   200   {object}  response.Response{data=configuration.PackingModal,msg=string}
// @Router    /packing/addPacking [post]
func (p *PackingApi) AddPacking(c *gin.Context) {
	var packingModal configuration.PackingModal
	if err := c.ShouldBindJSON(&packingModal); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if packing, err := configurationService.AddPacking(&packingModal); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("添加包装方式失败", zap.Error(err))
	} else {
		commonRes.OkWithDetailed(packing, "添加成功", c)
	}
}

// GetPacking
// @Tags      PackingApi
// @Summary   分页获取包装方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query      configurationReq.ApiSearchParams true  "请求参数"
// @Success   200   {object}  response.Response{data=response.PageResult{list=[]configuration.PackingModal},msg=string}
// @Router    /packing/getPacking [get]
func (p *PackingApi) GetPacking(c *gin.Context) {
	var packSp configurationReq.ApiSearchParams
	if err := c.ShouldBindQuery(&packSp); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if list, total, err := configurationService.GetPacking(packSp); err != nil {
		commonRes.FailWithMessage("包装方式列表获取失败", c)
		global.FreightLog.Error("包装方式列表获取失败", zap.Error(err))
	} else {
		commonRes.OkWithDetailed(commonRes.PageResult{
			List:     list,
			Total:    total,
			Page:     packSp.Page,
			PageSize: packSp.PageSize,
		}, "获取成功", c)
	}
}

// EditPacking
// @Tags      PackingApi
// @Summary   编辑包装方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configuration.PackingModal true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /packing/editPacking [put]
func (p *PackingApi) EditPacking(c *gin.Context) {
	var packingModal configuration.PackingModal
	if err := c.ShouldBindJSON(&packingModal); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.EditPacking(&packingModal); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("包装方式编辑失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// SwitchEnable
// @Tags      PackingApi
// @Summary   编辑启用方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.SwitchEnable true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /packing/switchEnable [put]
func (p *PackingApi) SwitchEnable(c *gin.Context) {
	var enable configurationReq.SwitchEnable
	if err := c.ShouldBindJSON(&enable); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.SwitchEnable(&enable); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("包装方式启用状态编辑失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// DeletePacking
// @Tags      PackingApi
// @Summary   删除包装方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CId true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /packing/delPacking [delete]
func (p *PackingApi) DeletePacking(c *gin.Context) {
	var cId commonReq.CId
	if err := c.ShouldBindJSON(&cId); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.DeletePacking(cId.ID); err != nil {
		commonRes.Fail(c)
		global.FreightLog.Error("删除失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// BatchDeletePacking
// @Tags      PackingApi
// @Summary   批量删除包装方式
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CIds true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /packing/batchDelPacking [delete]
func (p *PackingApi) BatchDeletePacking(c *gin.Context) {
	var cIds commonReq.CIds
	if err := c.ShouldBindJSON(&cIds); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.BatchDeletePacking(cIds.IDs); err != nil {
		commonRes.Fail(c)
		global.FreightLog.Error("包装方式批量删除失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

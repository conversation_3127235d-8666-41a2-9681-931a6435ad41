package configuration

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"server/global"
	commonReq "server/model/common/request"
	commonRes "server/model/common/response"
	configurationReq "server/model/configuration/request"
)

type CarLengthApi struct {
}

// GetCarLengthList
// @Tags      CarLengthApi
// @Summary   车长列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.CarLengthSearchParams true "请求参数"
// @Success   200   {object}  response.Response{data=[]configuration.CarTypeModal,msg=string}
// @Router    /carLength/carLengthList [get]
func (cl *CarLengthApi) GetCarLengthList(c *gin.Context) {
	var carLengthSp configurationReq.CarLengthSearchParams
	if err := c.ShouldBindQuery(&carLengthSp); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	list, err := configurationService.GetCarLengthList(carLengthSp)
	if err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("获取车长列表失败", zap.Error(err))
	} else {
		commonRes.OkWithDetailed(list, "获取车长列表成功", c)
	}
}

// AddCarLength
// @Tags      CarLengthApi
// @Summary   添加车长
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.CarLengthAdd true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /carLength/addCarLength [post]
func (cl *CarLengthApi) AddCarLength(c *gin.Context) {
	var carLength configurationReq.CarLengthAdd
	if err := c.ShouldBindJSON(&carLength); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.AddCarLength(&carLength); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("新增车长失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// EditCarLength
// @Tags      EditCarLength
// @Summary   编辑车长
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.CarLengthEdit true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /carLength/editCarLength [put]
func (cl *CarLengthApi) EditCarLength(c *gin.Context) {
	var carLength configurationReq.CarLengthEdit
	if err := c.ShouldBindJSON(&carLength); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.EditCarLength(&carLength); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("编辑车长失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// DeleteCarLength
// @Tags      DeleteCarLength
// @Summary   删除车长
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CId true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /carLength/deleteCarLength [delete]
func (cl *CarLengthApi) DeleteCarLength(c *gin.Context) {
	var cId commonReq.CId
	if err := c.ShouldBindJSON(&cId); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.DeleteCarLength(cId.ID); err != nil {
		commonRes.Fail(c)
		global.FreightLog.Error("删除失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

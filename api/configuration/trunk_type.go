package configuration

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"server/global"
	"server/model/common/response"
)

type TrunkTypeAPI struct {
}

func (t *TrunkTypeAPI) AddTrunkType(c *gin.Context) {

}

func (t *TrunkTypeAPI) RemoveTrunkType(c *gin.Context) {

}

func (t *TrunkTypeAPI) GetTrunkTypeList(c *gin.Context) {

}

func (t *TrunkTypeAPI) GetTrunkType(c *gin.Context) {
	if types, err := configurationService.GetTrunkType(); err != nil {
		response.FailWithMessage("获取车型失败", c)
		global.FreightLog.Error("货车类型列表获取失败", zap.Error(err))
	} else {
		response.OkWithDetailed(types, "成功", c)
	}
}

func (t *TrunkTypeAPI) UpdateTrunkType(c *gin.Context) {

}

func (t *TrunkTypeAPI) RemoveAllTrunkType(c *gin.Context) {

}

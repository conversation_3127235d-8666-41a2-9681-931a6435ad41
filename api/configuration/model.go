package configuration

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"server/global"
	commonReq "server/model/common/request"
	commonRes "server/model/common/response"
	configurationReq "server/model/configuration/request"
)

type ModelApi struct {
}

// AddModel
// @Tags      ModelApi
// @Summary   添加车型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.Type true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /model/addModel [post]
func (m *ModelApi) AddModel(c *gin.Context) {
	var typeSp configurationReq.Type
	if err := c.ShouldBindJSON(&typeSp); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if ty, err := configurationService.AddType(&typeSp); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("新增车型失败", zap.Error(err))
	} else {
		commonRes.OkWithDetailed(ty, "新增成功", c)
	}
}

// GetModel
// @Tags      ModelApi
// @Summary   车型列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=response.PageResult{list=[]configuration.CarTypeModal},msg=string}
// @Router    /model/getModel [get]
func (m *ModelApi) GetModel(c *gin.Context) {
	list, err := configurationService.GetTypes()
	if err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("获取车型列表失败", zap.Error(err))
	} else {
		commonRes.OkWithDetailed(list, "获取车型列表成功", c)
	}
}

// DeleteModel
// @Tags      ModelApi
// @Summary   删除车型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CId true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /model/delModel [delete]
func (m *ModelApi) DeleteModel(c *gin.Context) {
	var cId commonReq.CId
	if err := c.ShouldBindJSON(&cId); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.DeleteType(cId.ID); err != nil {
		commonRes.Fail(c)
		global.FreightLog.Error("车型删除失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// EditModel
// @Tags      ModelApi
// @Summary   编辑车型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.EditTypeReq true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /model/editModel [put]
func (m *ModelApi) EditModel(c *gin.Context) {
	var model configurationReq.EditTypeReq
	if err := c.ShouldBindJSON(&model); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.EditType(&model); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("车型编辑失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}
}

// SwitchEnable
// @Tags      ModelApi
// @Summary   编辑车型是否启用
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      configurationReq.SwitchEnable true "请求参数"
// @Success   200   {object}  response.Response{msg=string}
// @Router    /model/switchEnable [put]
func (m *ModelApi) SwitchEnable(c *gin.Context) {
	var enable configurationReq.SwitchEnable
	if err := c.ShouldBindJSON(&enable); err != nil {
		commonRes.FailReq(err.Error(), c)
		return
	}
	if err := configurationService.SwitchEnableModel(&enable); err != nil {
		commonRes.FailWithMessage(err.Error(), c)
		global.FreightLog.Error("车型启用状态编辑失败", zap.Error(err))
	} else {
		commonRes.Ok(c)
	}

}

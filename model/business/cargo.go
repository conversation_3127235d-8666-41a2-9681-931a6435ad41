package business

import "server/global"

// LoadingAddress 装货地址表 (Loading_Address)
type LoadingAddress struct {
	global.FreightModel
	ID           uint   `gorm:"primaryKey;comment:装货地址ID" json:"id"`
	Address      string `gorm:"type:varchar(255);comment:装货地址" json:"address"`
	ContactName  string `gorm:"type:varchar(100);comment:联系人姓名" json:"contact_name"`
	ContactPhone string `gorm:"type:varchar(20);comment:联系人电话" json:"contact_phone"`
}

// UnloadingAddress 卸货地址表 (Unloading_Address)
type UnloadingAddress struct {
	global.FreightModel
	ID           uint   `gorm:"primaryKey;comment:卸货地址ID" json:"id"`
	Address      string `gorm:"type:varchar(255);comment:卸货地址" json:"address"`
	ContactName  string `gorm:"type:varchar(100);comment:联系人姓名" json:"contact_name"`
	ContactPhone string `gorm:"type:varchar(20);comment:联系人电话" json:"contact_phone"`
}

// Cargo 货物表 (Cargo)
type Cargo struct {
	global.FreightModel
	ID          uint   `gorm:"primaryKey;comment:货物ID" json:"id"`
	Name        string `gorm:"type:varchar(255);comment:货物名称" json:"name"`
	Packaging   string `gorm:"type:varchar(100);comment:包装方式" json:"packaging"`
	WeightRange string `gorm:"type:varchar(100);comment:总重量区间" json:"weight_range"`
	VolumeRange string `gorm:"type:varchar(100);comment:体积区间" json:"volume_range"`
	Temperature string `gorm:"type:varchar(50);comment:温控范围" json:"temperature"`
}

// VehicleRequirement 车辆需求表 (Vehicle_Requirement)
type VehicleRequirement struct {
	global.FreightModel
	ID           uint   `gorm:"primaryKey;comment:需求ID" json:"id"`
	CargoID      uint   `gorm:"foreignKey;comment:货物ID" json:"cargo_id"`
	VehicleCount int    `gorm:"type:int;comment:所需车辆数" json:"vehicle_count"`
	VehicleType  string `gorm:"type:varchar(50);comment:用车类型" json:"vehicle_type"`
	CarLength    string `gorm:"type:varchar(255);comment:车长" json:"car_length"`
	VehicleModel string `gorm:"type:varchar(255);comment:车型" json:"vehicle_model"`
	LoadingTime  string `gorm:"type:varchar(100);comment:装货时间" json:"loading_time"`
	PriceType    string `gorm:"type:varchar(50);comment:价格类型" json:"price_type"`
	PriceUnit    string `gorm:"type:varchar(50);comment:价格单位" json:"price_unit"`
	Notes        string `gorm:"type:text;comment:备注" json:"notes"`
}

// LoadingUnloadingAssociation 装货卸货关联表 (Loading_Unloading_Association)
type LoadingUnloadingAssociation struct {
	global.FreightModel
	ID          uint `gorm:"primaryKey;comment:关联ID" json:"id"`
	LoadingID   uint `gorm:"foreignKey;comment:装货地址ID" json:"loading_id"`
	UnloadingID uint `gorm:"foreignKey;comment:卸货地址ID" json:"unloading_id"`
}

// VehicleTemperatureRequirement 车辆温控要求表 (Vehicle_Temperature_Requirement)
type VehicleTemperatureRequirement struct {
	global.FreightModel
	ID            uint   `gorm:"primaryKey;comment:车辆温控要求ID" json:"id"`
	RequirementID uint   `gorm:"foreignKey;comment:需求ID" json:"requirement_id"`
	Temperature   string `gorm:"type:varchar(50);comment:温控要求" json:"temperature"`
}

func (LoadingAddress) TableName() string {
	return "business_loading_address"
}

func (UnloadingAddress) TableName() string {
	return "business_unloading_address"
}

func (Cargo) TableName() string {
	return "business_cargo"
}

func (VehicleRequirement) TableName() string {
	return "business_vehicle_requirement"
}

func (LoadingUnloadingAssociation) TableName() string {
	return "business_loading_unloading_association"
}

func (VehicleTemperatureRequirement) TableName() string {
	return "business_vehicle_temperature_requirement"
}

package configuration

import "server/global"

type PackingModal struct {
	global.FreightModel
	TypeName    string `json:"typeName" gorm:"not null;comment:包装方式名称" binding:"required"`    //包装方式名称
	Description string `json:"description" gorm:"not null;comment:包装方式描述" binding:"required"` //包装方式描述
	Enable      bool   `json:"enable" gorm:"default:true;comment:是否启用"`                       //是否启用
}

func (PackingModal) TableName() string {
	return "configuration_packing"
}

package configuration

import "server/global"

type TruckTypeModal struct {
	global.FreightModel
	TypeID      int     `json:"type_id" gorm:"column:type_id;comment:车型ID" binding:"required"` // 车型ID
	Name        string  `json:"name" gorm:"not null;comment:车型名称" binding:"required"`          //车型名称
	Description string  `json:"description" gorm:"not null;comment:车型描述" binding:"required"`   //车型描述
	Enable      bool    `json:"enable" gorm:"default:true;comment:是否启用"`                       //是否启用
	Length      float64 `json:"length" gorm:"not null;comment:车身长度" binding:"required"`
	Height      float64 `json:"height" gorm:"not null;comment:车身高度" binding:"required"`
}

func (TruckTypeModal) TableName() string { return "truck_type_model" }

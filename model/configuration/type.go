package configuration

import "server/global"

type CarTypeModal struct {
	global.FreightModel
	Name        string        `json:"name" gorm:"not null;comment:车型名称"`        //车型名称
	Description string        `json:"description" gorm:"not null;comment:车型描述"` //车型描述
	Enable      bool          `json:"enable" gorm:"default:true;comment:是否启用"`  //是否启用
	CarLength   []LengthModal `json:"carLength" gorm:"many2many:model_length;"`
}

func (CarTypeModal) TableName() string { return "car_model" }

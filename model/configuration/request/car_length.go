package request

type CarLengthAdd struct {
	MinLength float64 `json:"minLength" gorm:"not null;"` //最小长度
	MaxLength float64 `json:"maxLength" gorm:"not null;"` //最大长度
	MinWidth  float64 `json:"minWidth" gorm:"not null;"`  //最小宽度
	MaxWidth  float64 `json:"maxWidth" gorm:"not null;"`  //最大宽度
	MinHeight float64 `json:"minHeight" gorm:"not null;"` //最小高度
	MaxHeight float64 `json:"maxHeight" gorm:"not null;"` //最大高度
	MinLoad   float64 `json:"minLoad" gorm:"not null;"`   //最小载重
	MaxLoad   float64 `json:"maxLoad" gorm:"not null;"`   //最大载重
	MinVolume float64 `json:"minVolume" gorm:"not null;"` //最小体积
	MaxVolume float64 `json:"maxVolume" gorm:"not null;"` //最大体积
	CarTypes  []uint  `json:"carTypes" gorm:"not null;"`  //车型id
}

type CarLengthSearchParams struct {
	MaxLength float64 `json:"maxLength" form:"maxLength"` //最大长度
	MaxWidth  float64 `json:"maxWidth" form:"maxWidth"`   //最大宽度
	MaxHeight float64 `json:"maxHeight" form:"maxHeight"` //最大高度
	MaxLoad   float64 `json:"maxLoad" form:"maxLoad"`     //最大载重
}

type CarLengthEdit struct {
	Id uint `json:"id" gorm:"primary_key;auto_increment"`
	CarLengthAdd
}

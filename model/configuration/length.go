package configuration

import "server/global"

type LengthModal struct {
	global.FreightModel
	MinLength float64        `json:"minLength" gorm:"not null;comment:最小长度" binding:"required"` //最小长度
	MaxLength float64        `json:"maxLength" gorm:"not null;comment:最大长度" binding:"required"` //最大长度
	MinWidth  float64        `json:"minWidth" gorm:"not null;comment:最小宽度" binding:"required"`  //最小宽度
	MaxWidth  float64        `json:"maxWidth" gorm:"not null;comment:最大宽度" binding:"required"`  //最大宽度
	MinHeight float64        `json:"minHeight" gorm:"not null;comment:最小高度" binding:"required"` //最小高度
	MaxHeight float64        `json:"maxHeight" gorm:"not null;comment:最大高度" binding:"required"` //最大高度
	MinLoad   float64        `json:"minLoad" gorm:"not null;comment:最小载重" binding:"required"`   //最小载重
	MaxLoad   float64        `json:"maxLoad" gorm:"not null;comment:最大载重" binding:"required"`   //最大载重
	MinVolume float64        `json:"minVolume" gorm:"not null;comment:最小体积" binding:"required"` //最小体积
	MaxVolume float64        `json:"maxVolume" gorm:"not null;comment:最大体积" binding:"required"` //最大体积
	Image     string         `json:"image" gorm:"comment:车型图片"`                                 //车型图片
	CarTypes  []CarTypeModal `json:"carTypes" gorm:"many2many:model_length;"`
}

func (LengthModal) TableName() string { return "car_length" }

package authority

import (
	"server/global"
)

type UserModel struct {
	global.FreightModel
	Username    string `json:"username" gorm:"index;unique;comment:用户名" binding:"required"` // 用户名
	Password    string `json:"-"  gorm:"not null;comment:密码"`
	Phone       string `json:"phone"  gorm:"comment:手机号"`                          // 手机号
	Email       string `json:"email"  gorm:"comment:邮箱" binding:"omitempty,email"` // 邮箱
	Active      bool   `json:"active"`                                             // 是否活跃
	RoleModelID uint   `json:"roleId" gorm:"not null" binding:"required"`          // 角色ID
}

func (UserModel) TableName() string {
	return "authority_user"
}

type ClientUserModel struct {
	global.FreightModel
	Phone    string `json:"phone" gorm:"not null;comment:手机号"`       //手机号
	Password string `json:"-" gorm:"not null;comment:密码"`            //密码
	Picture  string `json:"picture" gorm:"comment:头像"`               //头像
	Enable   bool   `json:"enable" gorm:"default:true;comment:是否禁用"` //是否禁用
	Source   int    `json:"source" gorm:"default:1;comment:来源"`      //来源 1:货主  2:司机
}

func (ClientUserModel) TableName() string { return "client_user" }

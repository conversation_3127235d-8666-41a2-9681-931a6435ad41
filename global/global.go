package global

import (
	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"

	"server/config"
)

var (
	FreightVp                 *viper.Viper
	FreightConfig             config.Server
	FreightLog                *zap.Logger
	FreightDb                 *gorm.DB
	FreightRedis              *redis.Client
	FreightConcurrencyControl = &singleflight.Group{}
	FreightCron               *cron.Cron
)

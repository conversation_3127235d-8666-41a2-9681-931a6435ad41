package internal

import (
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"path"

	"server/global"
)

type lumberjackLogs struct{}

var LumberjackLogs = new(lumberjackLogs)

// GetWriteSyncer 获取 zapcore.WriteSyncer
func (l *lumberjackLogs) GetWriteSyncer(level string) zapcore.WriteSyncer {
	fileWriter := &lumberjack.Logger{
		Filename:   path.Join(global.FreightConfig.Zap.Director, level+".log"),
		MaxSize:    global.FreightConfig.RotateLogs.MaxSize,
		MaxBackups: global.FreightConfig.RotateLogs.MaxBackups,
		MaxAge:     global.FreightConfig.RotateLogs.MaxAge,
		Compress:   global.FreightConfig.RotateLogs.Compress,
	}

	if global.FreightConfig.Zap.LogInConsole {
		return zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(fileWriter))
	}
	return zapcore.AddSync(fileWriter)
}

name: Build and Deploy

on:
  push:
    branches:
      - main
      - master  # 如果你的默认分支是 master，添加它

jobs:
  build-and-deploy:
    runs-on: ubuntu-22.04

    steps:
      # 1. 检出代码
      - name: Checkout code
        uses: actions/checkout@v3
#        uses: yohitech/dependencies/checkout@v3
        with:
          repository: Internal/transport-server-online
          ssh-key: ${{ secrets.SSH_PRIVATE_KEY }}  # 使用 SSH
          # token: ${{ secrets.GITEA_PAT }}        # 如果使用 PAT
          path: app
          ref: master  # 确认你的分支名是否是 master
      - name: Configure Go Proxy
        run: |
          go env -w GOPROXY=https://goproxy.cn,direct
      # 2. 安装构建工具（根据你的项目类型调整）
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.19'

      # 3. 编译代码
      - name: Build application
        run: |
          cd app
          go build -o transport-server

      # 4. 将应用传输到目标机器
      - name: Deploy to target server
        run: |
          set -x
          scp -o StrictHostKeyChecking=no app/transport-server root@*************:/opt || true
          scp -o StrictHostKeyChecking=no app/config.yaml root@*************:/opt || true
      # 5. 在目标机器上运行应用
      - name: Start application on target server
        run: |
          set -x
          ssh -o StrictHostKeyChecking=no root@************* "
            if pgrep -f transport-server > /dev/null; then
              pkill -9 -f transport-server;
              echo 'Old process stopped';
            else
              echo 'No running process found';
            fi
          "
          ssh -o StrictHostKeyChecking=no root@************* '
            nohup /opt/transport-server > ~/transport-server.log 2>&1 & || true
          '

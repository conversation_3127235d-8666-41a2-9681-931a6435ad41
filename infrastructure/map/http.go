package direction

import (
	"fmt"
	"net/url"
	"reflect"
)

const (
	AMapGeoURI   = "https://restapi.amap.com/v3/geocode/geo"
	AMapReGeoURI = "https://restapi.amap.com/v3/geocode/regeo"
)

// URLBuilder 定义一个接口，包含 BuildURL 方法
type URLBuilder interface {
	BuildURL(baseURL string) (string, error)
}

// BuildURLFromStruct 实现 URL 拼接的通用函数
func BuildURLFromStruct(baseURL string, s interface{}) (string, error) {
	// 使用 url.Values 存储参数
	params := url.Values{}

	// 使用 reflect 获取结构体字段和值
	v := reflect.ValueOf(s)
	t := v.Type()

	// 遍历结构体字段
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i).String()

		// 获取字段对应的 URL 参数名称
		tag := field.Tag.Get("url")

		// 如果字段有值且URL tag不为空，才将其加入请求参数
		if tag != "" && value != "" {
			params.Add(tag, value)
		}
	}

	// 拼接完整的请求URL
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	return fullURL, nil
}

type GeoRequest struct {
	Key      string `url:"key"` // 高德Key
	Address  string `url:"address"`
	City     string `url:"city"`
	Sig      string `url:"sig"` // 签名
	Output   string `url:"output"`
	CallBack string `url:"callback"` // 回调函数
}

// BuildURL 实现 URLBuilder 接口的 BuildURL 方法
func (gr GeoRequest) BuildURL(baseURL string) (string, error) {
	return BuildURLFromStruct(baseURL, gr)
}

type BaseResponse struct {
	Status int    `json:"status"` // 返回值为 0 或 1，0 表示请求失败；1 表示请求成功。
	Count  int    `json:"count"`
	Info   string `json:"info"`
}

type GeoCode struct {
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	CityCode string `json:"cityCode"`
	District string `json:"district"` // 区
	Street   string `json:"street"`   // 街道
	Number   string `json:"number"`   // 门牌号
	AdCode   string `json:"adCode"`   // 区域编码
	Location string `json:"location"`
	Level    string `json:"level"`
}

type GeoResponse struct {
	BaseResponse
	GeoCodes []GeoCode `json:"geocodes"`
}

type ReGeoRequest struct {
	Key        string `url:"key"`
	Location   string `url:"location"`
	POIType    string `url:"poitype"`
	Radius     string `url:"radius"`
	Extensions string `url:"extensions"`
	RoadLevel  int    `url:"roadlevel"`
	Sig        string `url:"sig"`
	Output     string `url:"output"`
	CallBack   string `url:"callback"`
	HomeOrCorp int    `url:"homeorcorp"`
}

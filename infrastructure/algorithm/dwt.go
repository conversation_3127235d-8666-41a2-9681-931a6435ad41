package algorithm

import (
	"math"
	"time"
)

type Point struct {
	Latitude  float64
	Longitude float64
	Timestamp time.Time
}

type Route []Point

// 计算两点之间的 Haversine 距离（单位：米）
func haversineDistance(p1, p2 Point) float64 {
	const earthRadius = 6371000.0 // 地球半径（米）
	dLat := (p2.Latitude - p1.Latitude) * math.Pi / 180.0
	dLon := (p2.Longitude - p1.Longitude) * math.Pi / 180.0

	lat1 := p1.Latitude * math.Pi / 180.0
	lat2 := p2.Latitude * math.Pi / 180.0

	a := math.Sin(dLat/2)*math.Sin(dLat/2) + math.Sin(dLon/2)*math.Sin(dLon/2)*math.Cos(lat1)*math.Cos(lat2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// 动态时间规整算法计算两个路线之间的相似度（返回最小距离）
func dtw(route1, route2 Route) float64 {
	n := len(route1)
	m := len(route2)

	// 创建 DP 数组
	dp := make([][]float64, n+1)
	for i := range dp {
		dp[i] = make([]float64, m+1)
		for j := range dp[i] {
			dp[i][j] = math.MaxFloat64
		}
	}
	dp[0][0] = 0

	// 动态规划计算最小距离
	for i := 1; i <= n; i++ {
		for j := 1; j <= m; j++ {
			dist := haversineDistance(route1[i-1], route2[j-1])
			dp[i][j] = dist + math.Min(dp[i-1][j], math.Min(dp[i][j-1], dp[i-1][j-1]))
		}
	}
	return dp[n][m]
}

// 检查两个路线的时间差是否在给定时间窗范围内（单位：秒）
func withinTimeWindow(route1, route2 Route, timeWindowSec int64) bool {
	startTime1 := route1[0].Timestamp
	endTime1 := route1[len(route1)-1].Timestamp

	startTime2 := route2[0].Timestamp
	endTime2 := route2[len(route2)-1].Timestamp

	// 检查时间窗重叠
	return !(endTime1.Before(startTime2.Add(-time.Duration(timeWindowSec)*time.Second)) ||
		startTime1.After(endTime2.Add(time.Duration(timeWindowSec)*time.Second)))
}

// 匹配路线
func matchRoutes(route1, route2 Route, timeWindowSec int64, maxDtwDistance float64) bool {
	// 先检查时间窗
	if !withinTimeWindow(route1, route2, timeWindowSec) {
		return false
	}

	// 再检查 DTW 距离
	dtwDistance := dtw(route1, route2)
	return dtwDistance <= maxDtwDistance
}

// 按照给定的距离（单位：米）对路线进行切割
func splitRouteByDistance(route Route, maxDistance float64) []Route {
	var segments []Route
	var currentSegment Route

	for i := 1; i < len(route); i++ {
		// 添加当前点到当前切片
		currentSegment = append(currentSegment, route[i-1])

		// 计算与下一个点的距离
		distance := haversineDistance(route[i-1], route[i])
		if distance >= maxDistance {
			// 如果距离大于最大距离，切割
			segments = append(segments, currentSegment)
			currentSegment = Route{}
		}
	}

	// 添加最后一个切片
	if len(currentSegment) > 0 {
		segments = append(segments, currentSegment)
	}
	return segments
}

func matchRoutesWithSegments(route1, route2 Route, timeWindowSec int64, maxDtwDistance float64, segmentDistance float64) bool {
	// 对两个路线进行切割
	segments1 := splitRouteByDistance(route1, segmentDistance)
	segments2 := splitRouteByDistance(route2, segmentDistance)

	// 遍历所有的段进行匹配
	for _, seg1 := range segments1 {
		for _, seg2 := range segments2 {
			if matchRoutes(seg1, seg2, timeWindowSec, maxDtwDistance) {
				return true // 有任意一段匹配即返回成功
			}
		}
	}
	return false // 所有段都没有匹配到则返回失败
}

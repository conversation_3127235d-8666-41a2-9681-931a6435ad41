package service

import (
	"server/service/authority"
	"server/service/base"
	"server/service/configuration"
	"server/service/file_m"
	"server/service/monitor"
	"server/service/sys_tool"
)

type ServiceGroup struct {
	Base          base.ServiceGroup
	Authority     authority.ServiceGroup
	FileM         file_m.ServiceGroup
	Monitor       monitor.ServiceGroup
	SysTool       sys_tool.ServiceGroup
	Configuration configuration.ServiceGroup
}

var ServiceGroupApp = new(ServiceGroup)

package base

import (
	"errors"
	"go.uber.org/zap"
	"server/global"
	modelAuthority "server/model/authority"
	"server/utils"
)

// 登录注册相关

type LogRegService struct{}

// Login 登陆校验
func (lr *LogRegService) Login(u *modelAuthority.UserModel) (userInter *modelAuthority.UserModel, err error) {
	var userModel modelAuthority.UserModel
	u.Password = utils.MD5V([]byte(u.Password))
	err = global.FreightDb.Where("username = ? AND password = ?", u.Username, u.Password).First(&userModel).Error
	if err != nil {
		return nil, errors.New("用户不存在或密码不正确")
	}
	if userModel.Active == false {
		return nil, errors.New("用户为禁用状态")
	}
	return &userModel, err
}

// ClientLogin C端登录校验
func (lr *LogRegService) ClientLogin(cLogin *modelAuthority.ClientUserModel) (userInter *modelAuthority.ClientUserModel, err error) {
	var clientUserModel modelAuthority.ClientUserModel
	err = global.FreightDb.Where("phone = ?", cLogin.Phone).First(&clientUserModel).Error
	if err == nil { //用户存在
		cLogin.Password = utils.MD5V([]byte(cLogin.Password))
		if clientUserModel.Password != cLogin.Password {
			return nil, errors.New("登录用户密码错误，请联系管理员")
		}
		if clientUserModel.Enable == false {
			return nil, errors.New("当前用户为禁用状态，请联系管理员")
		}
	} else {
		global.FreightLog.Info("C端登录用户不存在，走注册流程", zap.Error(err))
		clientUserModel.Phone = cLogin.Phone
		clientUserModel.Password = utils.MD5V([]byte(cLogin.Password))
		err = global.FreightDb.Create(&clientUserModel).Error
		if err != nil {
			global.FreightLog.Info("C端登录用户不存在，注册流程异常", zap.Error(err))
			return nil, err
		}
		return &clientUserModel, nil
	}
	return &clientUserModel, err
}

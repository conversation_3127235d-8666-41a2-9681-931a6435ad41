package configuration

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"server/global"
	"server/model/configuration"
	configurationReq "server/model/configuration/request"
	configurationRep "server/model/configuration/response"
	"strconv"
	"strings"
)

type CarLengthService struct {
}

// GetCarLengthList 获取车长列表
func (cl *CarLengthService) GetCarLengthList(carLengthSp configurationReq.CarLengthSearchParams) ([]configurationRep.CarLengthResponse, error) {
	db := global.FreightDb.Model(&configuration.LengthModal{})
	var carLengths []configuration.LengthModal
	if carLengthSp.MaxLength != 0 {
		maxLength := strconv.FormatFloat(carLengthSp.MaxLength, 'f', -1, 64)
		db = db.Where("CAST(max_length AS CHAR) LIKE ?", "%"+maxLength+"%")
	}
	if carLengthSp.MaxWidth != 0 {
		maxWidth := strconv.FormatFloat(carLengthSp.MaxWidth, 'f', -1, 64)
		db = db.Where("CAST(max_width AS CHAR) LIKE ?", "%"+maxWidth+"%")
	}
	if carLengthSp.MaxHeight != 0 {
		maxHeight := strconv.FormatFloat(carLengthSp.MaxHeight, 'f', -1, 64)
		db = db.Where("CAST(max_height AS CHAR) LIKE ?", "%"+maxHeight+"%")
	}
	if carLengthSp.MaxLoad != 0 {
		maxLoad := strconv.FormatFloat(carLengthSp.MaxLoad, 'f', -1, 64)
		db = db.Where("CAST(max_load AS CHAR) LIKE ?", "%"+maxLoad+"%")
	}
	err := db.Order("created_at asc").Preload("CarTypes").Find(&carLengths).Error
	var carLengthsList []configurationRep.CarLengthResponse
	data, _ := json.Marshal(carLengths)
	_ = json.Unmarshal(data, &carLengthsList)
	for i, carL := range carLengthsList {
		var names []string
		for _, ty := range carL.CarTypes {
			names = append(names, ty.Name)
		}
		carLengthsList[i].Names = strings.Join(names, "，")
	}
	return carLengthsList, err
}

// AddCarLength 添加车长
func (cl *CarLengthService) AddCarLength(carLength *configurationReq.CarLengthAdd) error {
	var carLengthModel configuration.LengthModal
	if !errors.Is(global.FreightDb.Where("min_length = ? and max_length = ?", carLength.MinLength, carLength.MaxLength).First(&carLengthModel).Error, gorm.ErrRecordNotFound) {
		return errors.New("存在相同车长")
	}
	var carTypes []configuration.CarTypeModal
	for _, carType := range carLength.CarTypes {
		carTypes = append(carTypes, configuration.CarTypeModal{
			FreightModel: global.FreightModel{ID: carType},
		})
	}
	carLengthC := &configuration.LengthModal{
		MinLength: carLength.MinLength,
		MaxLength: carLength.MaxLength,
		MinWidth:  carLength.MinWidth,
		MaxWidth:  carLength.MaxWidth,
		MinHeight: carLength.MinHeight,
		MaxHeight: carLength.MaxHeight,
		MinLoad:   carLength.MinLoad,
		MaxLoad:   carLength.MaxLoad,
		MinVolume: carLength.MinVolume,
		MaxVolume: carLength.MaxVolume,
		Image:     "",
		CarTypes:  carTypes,
	}
	err := global.FreightDb.Create(carLengthC).Error
	return err
}

// EditCarLength 编辑车长
func (cl *CarLengthService) EditCarLength(carLength *configurationReq.CarLengthEdit) (err error) {
	var tx *gorm.DB
	defer func() {
		if err != nil && tx != nil {
			global.FreightLog.Error("操作撤销", zap.Error(err))
			tx.Rollback()
		}
	}()
	tx = global.FreightDb.Begin()
	var carLengthModel configuration.LengthModal
	if errors.Is(global.FreightDb.Where("id = ?", carLength.Id).First(&carLengthModel).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	var carTypes []configuration.CarTypeModal
	for _, carType := range carLength.CarTypes {
		carTypes = append(carTypes, configuration.CarTypeModal{
			FreightModel: global.FreightModel{ID: carType},
		})
	}
	updates := &configuration.LengthModal{
		MinLength: carLength.MinLength,
		MaxLength: carLength.MaxLength,
		MinWidth:  carLength.MinWidth,
		MaxWidth:  carLength.MaxWidth,
		MinHeight: carLength.MinHeight,
		MaxHeight: carLength.MaxHeight,
		MinLoad:   carLength.MinLoad,
		MaxLoad:   carLength.MaxLoad,
		MinVolume: carLength.MinVolume,
		MaxVolume: carLength.MaxVolume,
		Image:     "",
	}
	if err = tx.Model(&carLengthModel).Updates(updates).Error; err != nil {
		return err
	}
	err = tx.Model(&carLengthModel).Association("CarTypes").Replace(&carTypes)
	if err != nil {
		return err
	}
	tx.Commit()
	return err
}

// DeleteCarLength 删除车长
func (cl *CarLengthService) DeleteCarLength(id uint) (err error) {
	var carLengthModel configuration.LengthModal
	if errors.Is(global.FreightDb.Where("id = ?", id).First(&carLengthModel).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	return global.FreightDb.Select(clause.Associations).Unscoped().Delete(&carLengthModel).Error
}

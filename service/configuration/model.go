package configuration

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"server/global"
	"server/model/configuration"
	configurationReq "server/model/configuration/request"
)

type TypeService struct {
}

// AddType 添加车型
func (t *TypeService) AddType(ty *configurationReq.Type) (configuration.CarTypeModal, error) {
	var typeModel configuration.CarTypeModal
	if !errors.Is(global.FreightDb.Where("name = ?", ty.Name).First(&typeModel).Error, gorm.ErrRecordNotFound) {
		return typeModel, errors.New("存在相同车型")
	}

	typeModel.Name = ty.Name
	typeModel.Description = ty.Description

	err := global.FreightDb.Create(&typeModel).Error
	return typeModel, err
}

// GetTypes 获取车型列表
func (t *TypeService) GetTypes() ([]configuration.CarTypeModal, error) {
	var carTypeModals []configuration.CarTypeModal
	err := global.FreightDb.Order("id asc").Preload("CarLength").Find(&carTypeModals).Error
	return carTypeModals, err
}

// DeleteType 删除车型
func (t *TypeService) DeleteType(id uint) (err error) {
	var carTypeModel configuration.CarTypeModal
	if errors.Is(global.FreightDb.Where("id = ?", id).First(&carTypeModel).Error, gorm.ErrRecordNotFound) {
		global.FreightLog.Error("删除车型", zap.Error(err))
		return err
	}
	return global.FreightDb.Select(clause.Associations).Unscoped().Delete(&carTypeModel).Error
}

// EditType 编辑车型
func (t *TypeService) EditType(ty *configurationReq.EditTypeReq) error {
	var modelModal configuration.CarTypeModal
	if errors.Is(global.FreightDb.Where("id = ?", ty.ID).First(&modelModal).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	if modelModal.Name == ty.Name {
		return errors.New("存在相同车型")
	}
	return global.FreightDb.Model(&modelModal).Updates(map[string]interface{}{"name": ty.Name, "description": ty.Description}).Error
}

// SwitchEnableModel 编辑车型是否启用
func (t *TypeService) SwitchEnableModel(switchEnable *configurationReq.SwitchEnable) error {
	var modelModal configuration.CarTypeModal
	if errors.Is(global.FreightDb.Where("id = ?", switchEnable.ID).First(&modelModal).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	return global.FreightDb.Model(&modelModal).Update("enable", switchEnable.Enable).Error
}

package configuration

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"server/global"
	"server/model/configuration"
)

type TrunkTypeService struct{}

type TrunkName struct {
	TypeID int    `json:"typeId"`
	Name   string `json:"name"`
}

type TrunkLength struct {
	TypeID int     `json:"typeId"`
	Length float64 `json:"length"`
}

type TrunkTypes struct {
	Names   *[]TrunkName   `json:"names"`
	Lengths *[]TrunkLength `json:"lengths"`
}

func (t *TrunkTypeService) AddTrunkType(tt *configuration.TruckTypeModal) error {

	return nil
}

func (t *TrunkTypeService) DeleteTrunkType() {

}

func (t *TrunkTypeService) ListTrunkType() (err error) {
	var trunkTypes []configuration.TruckTypeModal
	if errors.Is(global.FreightDb.Model(&trunkTypes).Find(&trunkTypes).Error, gorm.ErrRecordNotFound) {
		global.FreightLog.Error("没有任何记录", zap.Error(err))
		return err
	}
	return nil
}

// GetTrunkType 获取货车类型图
func (t *TrunkTypeService) GetTrunkType() (*TrunkTypes, error) {
	var trunkTypes TrunkTypes
	names, err := t.ListByNameGroup()
	if err != nil {
		return nil, err
	}
	trunkTypes.Names = names
	lengths, errL := t.ListByLengthGroup()
	if errL != nil {
		return nil, errL
	}
	trunkTypes.Lengths = lengths
	//jsonData, errJson := json.Marshal(trunkTypes)
	//if errJson != nil {
	//	fmt.Println(123, errJson)
	//}
	//fmt.Println(11, string(jsonData))
	return &trunkTypes, nil
}

func (t *TrunkTypeService) UpdateTrunkType() {

}

func (t *TrunkTypeService) BenchDeleteTrunkType() {}

func (t *TrunkTypeService) ListByNameGroup() (*[]TrunkName, error) {
	var trunkNames []TrunkName
	err := global.FreightDb.Model(&configuration.TruckTypeModal{}).
		Select("type_id AS TypeID , name AS Name").
		Group("name").
		Scan(&trunkNames).Error
	return &trunkNames, err
}

func (t *TrunkTypeService) ListByLengthGroup() (*[]TrunkLength, error) {
	var trunkLengths []TrunkLength
	err := global.FreightDb.Model(&configuration.TruckTypeModal{}).
		Select("type_id AS TypeID , length AS Length").
		Group("length").
		Scan(&trunkLengths).Error
	return &trunkLengths, err
}

func (t *TrunkTypeService) GetTrunkTypeByGetTrunkTypeByTypeIdAndLengthTypeIdAndLength(trunkTypeId int, length int) (*configuration.TruckTypeModal, error) {
	var trunkType configuration.TruckTypeModal
	global.FreightDb.Model(&configuration.TruckTypeModal{}).
		Select("type_id, name").
		Where("type_id = ? AND length = ?", trunkTypeId, length).
		Scan(&trunkType)
	return &trunkType, nil
}

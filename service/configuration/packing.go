package configuration

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"server/global"
	"server/model/configuration"
	configurationReq "server/model/configuration/request"
)

type PackService struct {
}

// AddPacking 添加包装方式
func (p *PackService) AddPacking(packing *configuration.PackingModal) (*configuration.PackingModal, error) {
	if !errors.Is(global.FreightDb.Where("type_name = ?", packing.TypeName).First(&configuration.PackingModal{}).Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("存在相同包装方式")
	}
	err := global.FreightDb.Create(packing).Error
	return packing, err
}

// GetPacking 获取包装方式列表
func (p *PackService) GetPacking(packSp configurationReq.ApiSearchParams) ([]configuration.PackingModal, int64, error) {
	limit := packSp.PageSize
	offset := packSp.PageSize * (packSp.Page - 1)
	db := global.FreightDb.Model(&configuration.PackingModal{})
	var packingList []configuration.PackingModal
	if packSp.TypeName != "" {
		db = db.Where("type_name LIKE ?", "%"+packSp.TypeName+"%")
	}
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return packingList, total, err
	} else {
		db.Limit(limit).Offset(offset).Order("created_at desc")
		err = db.Find(&packingList).Error
	}
	return packingList, total, err
}

// EditPacking //编辑包装方式
func (p *PackService) EditPacking(packing *configuration.PackingModal) (err error) {
	var packModal configuration.PackingModal
	if errors.Is(global.FreightDb.Where("id = ?", packing.ID).First(&packModal).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	if packModal.TypeName == packing.TypeName {
		return errors.New("存在相同包装方式")
	}
	return global.FreightDb.Model(&packModal).Updates(map[string]interface{}{"type_name": packing.TypeName, "description": packing.Description}).Error
}

// SwitchEnable //编辑包装方式
func (p *PackService) SwitchEnable(switchEnable *configurationReq.SwitchEnable) (err error) {
	var packingModal configuration.PackingModal
	if errors.Is(global.FreightDb.Where("id = ?", switchEnable.ID).First(&packingModal).Error, gorm.ErrRecordNotFound) {
		return errors.New("记录不存在")
	}
	return global.FreightDb.Model(&packingModal).Update("enable", switchEnable.Enable).Error
}

// DeletePacking //删除包装方式
func (p *PackService) DeletePacking(id uint) (err error) {
	var packingModal configuration.PackingModal
	if errors.Is(global.FreightDb.Where("id = ?", id).First(&packingModal).Error, gorm.ErrRecordNotFound) {
		global.FreightLog.Error("删除包装方式", zap.Error(err))
		return err
	}
	return global.FreightDb.Unscoped().Delete(&packingModal).Error
}

// BatchDeletePacking 批量删除
func (p *PackService) BatchDeletePacking(ids []uint) (err error) {
	var packingModels []configuration.PackingModal
	return global.FreightDb.Find(&packingModels, "id in ?", ids).Unscoped().Delete(&packingModels).Error
}

package initialize

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"server/global"
)

func Redis() {
	redisCfg := global.FreightConfig.Redis
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", global.FreightConfig.Redis.Host, global.FreightConfig.Redis.Port),
		Password: redisCfg.Password,
		DB:       redisCfg.DB,
	})
	pong, err := client.Ping(context.Background()).Result()
	if err != nil {
		global.FreightLog.Error("redis connect ping failed, err:", zap.Error(err))
	} else {
		global.FreightLog.Info("redis connect ping response:", zap.String("pong", pong))
		global.FreightRedis = client
	}
}

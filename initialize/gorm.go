package initialize

import (
	"fmt"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"server/model/business"
	"server/model/configuration"
	"time"

	"server/global"
	modelAuthority "server/model/authority"
	modelFileM "server/model/file_m"
	modelMonitor "server/model/monitor"
	modelSysTool "server/model/sys_tool"
)

type Writer struct {
	logger.Writer
}

// NewWriter writer 构造函数
func NewWriter(w logger.Writer) *Writer {
	return &Writer{Writer: w}
}

// Printf 格式化打印日志
func (w *Writer) Printf(message string, data ...interface{}) {
	if global.FreightConfig.Pgsql.LogZap {
		global.FreightLog.Info(fmt.Sprintf(message+"\n", data...))
	} else {
		w.Writer.Printf(message, data...)
	}
}

func gormConfig() *gorm.Config {
	newLogger := logger.New(
		NewWriter(log.New(os.Stdout, "\r\n", log.LstdFlags)), // io writer（日志输出的目标，前缀和日志包含的内容——译者注）
		logger.Config{
			SlowThreshold:             200 * time.Millisecond, // 慢 SQL 阈值
			LogLevel:                  logger.Warn,            // 日志级别
			IgnoreRecordNotFoundError: true,                   // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  false,                  // 禁用彩色打印
		},
	)
	config := &gorm.Config{
		Logger:                                   newLogger,
		DisableForeignKeyConstraintWhenMigrating: true,
	}
	return config
}

// Gorm 初始化数据库并产生数据库全局变量
func Gorm() *gorm.DB {
	p := global.FreightConfig.Pgsql
	if p.Dbname == "" {
		return nil
	}

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s %s",
		p.Host, p.Username, p.Password, p.Dbname, p.Port, p.Config)

	if db, err := gorm.Open(postgres.Open(dsn), gormConfig()); err != nil {
		global.FreightLog.Error("postgresql连接失败", zap.Error(err))
		return nil
	} else {
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(p.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.MaxOpenConns)
		return db
	}
}

// RegisterTables 初始化数据库表
func RegisterTables(db *gorm.DB) {
	err := db.AutoMigrate(
		// 权限
		modelAuthority.UserModel{},
		modelAuthority.RoleModel{},
		modelAuthority.MenuModel{},
		modelAuthority.ApiModel{},
		// 监控
		modelMonitor.OperationLogModel{},
		// file_m
		modelFileM.FileModel{},
		// 系统工具
		modelSysTool.CronModel{},
		//	基础配置管理
		&configuration.PackingModal{},
		&configuration.CarTypeModal{},
		&configuration.LengthModal{},
		&configuration.TruckTypeModal{},
		//	业务
		&business.Cargo{},
		&business.LoadingAddress{},
		&business.UnloadingAddress{},
		&business.VehicleRequirement{},
		&business.VehicleTemperatureRequirement{},
		&business.VehicleTemperatureRequirement{},
	)

	if err != nil {
		global.FreightLog.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}
	global.FreightLog.Info("register table success")
}

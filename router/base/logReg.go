package base

import (
	"github.com/gin-gonic/gin"
	"server/api"
)

type LogRegRouter struct{}

func (br *LogRegRouter) InitLogRegRouter(Router *gin.RouterGroup) (R gin.IRouter) {
	logRegRouter := Router.Group("logReg")

	logRegApi := api.GroupApp.Base.LogRegApi
	{
		logRegRouter.POST("captcha", logRegApi.Captcha)
		logRegRouter.POST("login", logRegApi.Login)
		logRegRouter.POST("logout", logRegApi.LogOut)
	}

	return logRegRouter
}

func (br *LogRegRouter) ClientInitLogRegRouter(Router *gin.RouterGroup) {
	clientLogRegRouter := Router.Group("logReg")
	logRegApi := api.GroupApp.Base.LogRegApi
	{
		clientLogRegRouter.POST("clientLogin", logRegApi.ClientLogin)   //登录
		clientLogRegRouter.POST("clientLogout", logRegApi.ClientLogout) //退出登录
	}
}

package configuration

import (
	"github.com/gin-gonic/gin"
	"server/api"
	"server/middleware"
)

type PackingRouter struct {
}

func (p *PackingRouter) InitPackingRouter(Router *gin.RouterGroup) {
	packingRouter := Router.Group("packing").Use(middleware.OperationRecord())
	packingWithoutRouter := Router.Group("packing")
	packingApi := api.GroupApp.Configuration.PackingApi
	{
		packingRouter.POST("addPacking", packingApi.AddPacking)                //添加包装方式
		packingRouter.PUT("editPacking", packingApi.EditPacking)               //编辑包装方式
		packingRouter.PUT("switchEnable", packingApi.SwitchEnable)             //编辑包装方式启动状态
		packingRouter.DELETE("delPacking", packingApi.DeletePacking)           //删除包装方式
		packingRouter.DELETE("batchDelPacking", packingApi.BatchDeletePacking) //批量删除包装方式
	}
	{
		packingWithoutRouter.GET("getPacking", packingApi.GetPacking) //分页获取包装方式
	}
}

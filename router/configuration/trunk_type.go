package configuration

import (
	"github.com/gin-gonic/gin"
	"server/api"
	"server/middleware"
)

type TrunkTypeRouter struct {
}

func (p *TrunkTypeRouter) InitTrunkTypeRouter(Router *gin.RouterGroup) {
	// TODO 哪些路由不实用jwt再研究
	trunkTypeRouter := Router.Group("packing").Use(middleware.OperationRecord())
	trunkTypeWithoutRouter := Router.Group("packing")
	trunkTypeAPI := api.GroupApp.Configuration.TrunkTypeAPI
	{
		trunkTypeRouter.POST("addTrunkType", trunkTypeAPI.AddTrunkType)              //添加车型
		trunkTypeRouter.PUT("editTrunkType", trunkTypeAPI.UpdateTrunkType)           //编辑车型
		trunkTypeRouter.DELETE("delTrunkType", trunkTypeAPI.RemoveTrunkType)         //删除车型
		trunkTypeRouter.DELETE("batchDelTrunkType", trunkTypeAPI.RemoveAllTrunkType) //批量删除车型
	}
	{
		trunkTypeWithoutRouter.GET("getTrunkType", trunkTypeAPI.GetTrunkType) //分页获取包装方式
	}
}

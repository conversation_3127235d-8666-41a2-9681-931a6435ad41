package configuration

import (
	"github.com/gin-gonic/gin"
	"server/api"
	"server/middleware"
)

type CarLengthRouter struct {
}

func (c *CarLengthRouter) InitCarLengthRouter(Router *gin.RouterGroup) {
	carLengthRouter := Router.Group("carLength").Use(middleware.OperationRecord())
	carLengthWithoutRouter := Router.Group("carLength")
	carLengthApi := api.GroupApp.Configuration.CarLengthApi
	{
		carLengthRouter.POST("addCarLength", carLengthApi.AddCarLength)         //添加车长
		carLengthRouter.PUT("editCarLength", carLengthApi.EditCarLength)        //编辑车长
		carLengthRouter.DELETE("deleteCarLength", carLengthApi.DeleteCarLength) //删除车长
	}
	{
		carLengthWithoutRouter.GET("carLengthList", carLengthApi.GetCarLengthList) //车长列表
	}
}

func (c *CarLengthRouter) ClientInitCarLengthRouter(Router *gin.RouterGroup) {
	carLengthRouter := Router.Group("carLength")
	carLengthApi := api.GroupApp.Configuration.CarLengthApi
	{
		carLengthRouter.GET("getCarLengthList", carLengthApi.GetCarLengthList) //车长列表
	}
}

package configuration

import (
	"github.com/gin-gonic/gin"
	"server/api"
	"server/middleware"
)

type ModelRouter struct {
}

func (m *ModelRouter) InitModelRouter(Router *gin.RouterGroup) {
	modelRouter := Router.Group("model").Use(middleware.OperationRecord())
	modelWithoutRouter := Router.Group("model")
	modelApi := api.GroupApp.Configuration.ModelApi
	{
		modelRouter.POST("addModel", modelApi.AddModel)        //添加车型
		modelRouter.DELETE("delModel", modelApi.DeleteModel)   //删除车型
		modelRouter.PUT("editModel", modelApi.EditModel)       //编辑车型
		modelRouter.PUT("switchEnable", modelApi.SwitchEnable) //编辑车型是否启用
	}
	{
		modelWithoutRouter.GET("getModel", modelApi.GetModel) //获取车型列表
	}
}

func (m *ModelRouter) ClientInitModelRouter(Router *gin.RouterGroup) {
	modelRouter := Router.Group("model")
	modelApi := api.GroupApp.Configuration.ModelApi
	{
		modelRouter.GET("getModel", modelApi.GetModel) //获取车型列表
	}
}

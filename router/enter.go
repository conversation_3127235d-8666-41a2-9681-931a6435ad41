package router

import (
	"server/router/authority"
	"server/router/base"
	"server/router/configuration"
	"server/router/file_m"
	"server/router/sys_tool"

	//"server/router/fileM"
	"server/router/monitor"
	//"server/router/sysTool"
)

type RouterGroup struct {
	Base          base.RouterGroup
	Authority     authority.RouterGroup
	FileM         file_m.RouterGroup
	Monitor       monitor.RouterGroup
	SysTool       sys_tool.RouterGroup
	Configuration configuration.RouterGroup
}

var RouterGroupApp = new(RouterGroup)

/*
 PostgreSQL Data Transfer - Converted from MySQL
 
 Source Server Type    : MySQL
 Target Server Type    : PostgreSQL
 Source Schema         : freight_server
 
 Conversion Date: 2025-08-29 10:53:19
*/

-- PostgreSQL specific settings
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- ----------------------------
-- Table structure for authority_api
-- ----------------------------
DROP TABLE IF EXISTS authority_api CASCADE;
CREATE TABLE authority_api (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  path VARCHAR(191) NOT NULL,
  description VARCHAR(191) NOT NULL,
  api_group VARCHAR(191) NOT NULL,
  method VARCHAR(191) NOT NULL DEFAULT 'POST'
);

COMMENT ON COLUMN authority_api.path IS 'api路径';
COMMENT ON COLUMN authority_api.description IS 'api中文描述';
COMMENT ON COLUMN authority_api.api_group IS 'api组';
COMMENT ON COLUMN authority_api.method IS '方法';

CREATE INDEX idx_sys_api_deleted_at ON authority_api (deleted_at);
CREATE INDEX idx_authority_api_deleted_at ON authority_api (deleted_at);

-- ----------------------------
-- Records of authority_api
-- ----------------------------
BEGIN;
INSERT INTO authority_api (id, created_at, updated_at, deleted_at, path, description, api_group, method) VALUES 
(1, '2023-03-10 06:24:36', '2024-01-24 08:59:10', NULL, '/logReg/captcha', '获取验证码（必选）', 'logReg', 'POST'),
(2, '2023-03-08 06:36:24', '2024-01-24 08:59:21', NULL, '/logReg/login', '登录（必选）', 'logReg', 'POST'),
(3, '2024-06-09 15:32:30', '2024-06-09 15:32:30', NULL, '/logReg/logout', '登出（必选）', 'logReg', 'POST'),
(4, '2023-03-10 07:21:37', '2023-03-10 07:21:37', NULL, '/casbin/editCasbin', '编辑casbin规则', 'casbin', 'POST'),
(5, '2023-03-08 08:56:13', '2023-03-10 07:11:53', NULL, '/user/getUserInfo', '获取用户信息（必选）', 'user', 'GET'),
(6, '2023-03-08 08:56:54', '2023-03-08 08:56:54', NULL, '/user/getUsers', '获取所有用户', 'user', 'POST'),
(7, '2023-03-10 06:41:32', '2023-03-10 06:41:32', NULL, '/user/deleteUser', '删除用户', 'user', 'POST'),
(8, '2023-03-10 06:42:24', '2023-03-10 06:42:24', NULL, '/user/addUser', '添加用户', 'user', 'POST'),
(9, '2023-03-10 06:47:18', '2023-03-10 06:47:18', NULL, '/user/editUser', '编辑用户', 'user', 'POST'),
(10, '2023-03-10 06:47:59', '2023-03-10 06:47:59', NULL, '/user/modifyPass', '修改用户密码', 'user', 'POST'),
(11, '2023-03-10 06:48:43', '2023-03-10 06:48:43', NULL, '/user/switchActive', '切换用户状态', 'user', 'POST'),
(12, '2023-03-10 06:58:30', '2023-03-10 06:58:30', NULL, '/role/getRoles', '获取所有角色', 'role', 'POST'),
(13, '2023-03-10 06:59:08', '2023-03-10 06:59:08', NULL, '/role/addRole', '添加角色', 'role', 'POST'),
(14, '2023-03-10 06:59:54', '2023-03-10 06:59:54', NULL, '/role/deleteRole', '删除角色', 'role', 'POST'),
(15, '2023-03-10 07:00:14', '2023-03-10 07:00:53', NULL, '/role/editRole', '编辑角色', 'role', 'POST'),
(16, '2023-03-10 07:01:44', '2023-03-10 07:01:44', NULL, '/role/editRoleMenu', '编辑角色菜单', 'role', 'POST'),
(17, '2023-03-10 07:14:44', '2023-03-10 07:14:44', NULL, '/menu/getMenus', '获取所有菜单', 'menu', 'GET'),
(18, '2023-03-10 07:15:25', '2023-03-10 07:15:25', NULL, '/menu/addMenu', '添加菜单', 'menu', 'POST'),
(19, '2023-03-10 07:15:50', '2023-03-10 07:15:50', NULL, '/menu/editMenu', '编辑菜单', 'menu', 'POST'),
(20, '2023-03-10 07:16:18', '2023-03-10 07:16:18', NULL, '/menu/deleteMenu', '删除菜单', 'menu', 'POST'),
(21, '2023-03-10 07:17:13', '2023-03-10 07:17:13', NULL, '/menu/getElTreeMenus', '获取所有菜单（el-tree结构）', 'menu', 'POST'),
(22, '2023-03-10 07:23:21', '2023-03-10 07:33:01', NULL, '/api/addApi', '添加api', 'api', 'POST'),
(23, '2023-03-10 07:24:00', '2023-03-10 07:24:00', NULL, '/api/getApis', '获取所有api', 'api', 'POST'),
(24, '2023-03-10 07:24:33', '2023-03-10 07:24:33', NULL, '/api/deleteApi', '删除api', 'api', 'POST'),
(25, '2023-03-10 07:26:15', '2023-03-10 07:26:15', NULL, '/api/editApi', '编辑api', 'api', 'POST'),
(26, '2023-03-10 07:34:08', '2023-03-10 07:35:04', NULL, '/api/getElTreeApis', '获取所有api（el-tree结构）', 'api', 'POST'),
(27, '2024-01-03 06:20:38', '2024-01-03 06:20:38', NULL, '/api/deleteApiById', '批量删除API', 'api', 'POST'),
(28, '2023-07-13 02:32:16', '2024-01-20 04:50:50', NULL, '/opl/getOplList', '分页获取操作记录', 'opl', 'POST'),
(29, '2023-07-13 02:33:32', '2024-01-20 04:54:16', NULL, '/opl/deleteOpl', '删除操作记录', 'opl', 'POST'),
(30, '2023-07-13 06:48:47', '2024-01-20 04:54:23', NULL, '/opl/deleteOplByIds', '批量删除操作记录', 'opl', 'POST'),
(31, '2023-08-27 06:05:00', '2023-08-27 06:05:00', NULL, '/file/upload', '文件上传', 'file', 'POST'),
(32, '2023-08-27 06:06:43', '2023-08-27 06:06:43', NULL, '/file/getFileList', '分页获取文件信息', 'file', 'POST'),
(33, '2024-01-04 03:10:15', '2024-01-04 03:10:41', NULL, '/file/download', '下载文件', 'file', 'GET'),
(34, '2024-01-04 03:16:04', '2024-01-04 03:16:04', NULL, '/file/delete', '删除文件', 'file', 'GET'),
(35, '2024-02-23 08:31:57', '2024-02-23 08:31:57', NULL, '/cron/getCronList', '分页获取cron', 'cron', 'POST'),
(36, '2024-02-23 08:33:56', '2024-02-23 08:33:56', NULL, '/cron/addCron', '添加cron', 'cron', 'POST'),
(37, '2024-02-23 08:34:25', '2024-02-23 08:34:25', NULL, '/cron/deleteCron', '删除cron', 'cron', 'POST'),
(38, '2024-02-23 08:34:50', '2024-02-23 08:34:50', NULL, '/cron/editCron', '编辑cron', 'cron', 'POST'),
(39, '2024-02-23 08:35:21', '2024-02-23 08:35:21', NULL, '/cron/switchOpen', 'cron开关', 'cron', 'POST'),
(40, '2024-09-27 09:42:20', '2024-09-27 09:42:20', NULL, '/packing/addPacking', '新建包装方式', 'configuration', 'POST'),
(41, '2024-09-27 15:59:15', '2024-09-27 15:59:15', NULL, '/packing/getPacking', '获取包装方式列表', 'configuration', 'GET'),
(42, '2024-09-28 11:47:24', '2024-09-28 11:47:24', NULL, '/packing/editPacking', '包装方式编辑', 'configuration', 'PUT'),
(43, '2024-09-28 12:19:51', '2024-09-28 12:19:51', NULL, '/packing/switchEnable', '修改包装方式启用状态', 'configuration', 'PUT'),
(44, '2024-09-28 13:04:11', '2024-09-28 13:04:11', NULL, '/packing/delPacking', '删除包装方式', 'configuration', 'DELETE'),
(45, '2024-09-28 13:15:03', '2024-09-28 15:26:51', NULL, '/packing/batchDelPacking', '批量删除包装方式', 'configuration', 'DELETE'),
(46, '2024-10-09 13:53:42', '2024-10-09 13:53:42', NULL, '/model/getModel', '获取车型列表', 'configuration', 'GET'),
(47, '2024-10-09 14:23:29', '2024-10-09 14:23:29', NULL, '/model/addModel', '新增车型', 'configuration', 'POST'),
(48, '2024-10-09 15:01:35', '2024-10-09 15:01:35', NULL, '/model/delModel', '删除车型', 'configuration', 'DELETE'),
(49, '2024-10-16 11:18:51', '2024-10-16 11:18:51', NULL, '/packing/getTrunkType', '车长车型', 'configuration', 'GET'),
(50, '2024-11-06 07:51:56', '2024-11-06 07:51:56', NULL, '/model/editModel', '编辑车型', 'configuration', 'PUT'),
(51, '2024-11-06 09:32:53', '2024-11-06 09:32:53', NULL, '/model/switchEnable', '编辑车型启动状态', 'configuration', 'PUT'),
(52, '2024-11-13 09:14:23', '2024-11-13 09:14:23', NULL, '/carLength/carLengthList', '获取车长列表', 'configuration', 'GET'),
(53, '2024-11-13 09:14:54', '2024-11-13 09:14:54', NULL, '/carLength/addCarLength', '添加车长', 'configuration', 'POST'),
(54, '2024-11-18 07:40:49', '2024-11-18 07:40:49', NULL, '/carLength/editCarLength', '编辑车长', 'configuration', 'PUT'),
(55, '2024-11-19 03:15:59', '2024-11-19 03:15:59', NULL, '/carLength/deleteCarLength', '删除车长', 'configuration', 'DELETE');

-- Set the sequence to continue from the last inserted ID
SELECT setval('authority_api_id_seq', 56);
COMMIT;

-- ----------------------------
-- Table structure for authority_menu
-- ----------------------------
DROP TABLE IF EXISTS authority_menu CASCADE;
CREATE TABLE authority_menu (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  pid BIGINT DEFAULT NULL,
  name VARCHAR(191) DEFAULT NULL,
  path VARCHAR(191) DEFAULT NULL,
  redirect VARCHAR(191) DEFAULT NULL,
  component VARCHAR(191) NOT NULL,
  meta JSONB DEFAULT NULL,
  sort BIGINT NOT NULL,
  UNIQUE (path)
);

CREATE INDEX idx_sys_menu_deleted_at ON authority_menu (deleted_at);
CREATE INDEX idx_authority_menu_deleted_at ON authority_menu (deleted_at);

-- ----------------------------
-- Records of authority_menu
-- ----------------------------
BEGIN;
INSERT INTO authority_menu (id, created_at, updated_at, deleted_at, pid, name, path, redirect, component, meta, sort) VALUES
(1, NULL, '2024-01-22 07:32:30', NULL, 0, 'Authority', '/authority', '/authority/user', 'Layout', '{"title": "权限管理", "svgIcon": "lock"}', 1),
(2, NULL, '2024-02-05 09:38:41', NULL, 1, 'User', 'user', '', 'authority/user/index.vue', '{"title": "用户管理"}', 1),
(3, NULL, '2023-06-28 08:12:06', NULL, 1, 'Role', 'role', '', 'authority/role/index.vue', '{"title": "角色管理"}', 2),
(4, NULL, '2023-06-28 08:12:16', NULL, 1, 'Menu', 'menu', '', 'authority/menu/index.vue', '{"title": "菜单管理"}', 3),
(5, '2023-03-07 01:50:48', '2023-06-28 08:11:38', NULL, 1, 'Api', 'api', '', 'authority/api/index.vue', '{"title": "接口管理"}', 4),
(6, NULL, '2023-08-25 09:55:12', NULL, 0, 'Cenu', '/cenu', '/cenu/cenu1', 'Layout', '{"title": "多级菜单", "svgIcon": "menu", "alwaysShow": true}', 2),
(7, NULL, '2024-09-26 09:00:58', NULL, 6, 'Cenu1', 'cenu1', '/cenu/cenu1/cenu1-1', 'Layout', '{"title": "cenu1", "alwaysShow": true}', 1),
(8, NULL, '2024-09-26 06:51:23', NULL, 7, 'Cenu1-1', 'cenu1-1', '', 'cenu/cenu1/cenu1-1/index.vue', '{"title": "cenu1-1"}', 1),
(9, '2023-03-13 06:14:27', '2024-09-26 08:18:46', NULL, 7, 'Cenu1-2', 'cenu1-2', '', 'cenu/cenu1/cenu1-2/index.vue', '{"title": "cenu1-2", "hidden": true}', 2),
(10, '2023-08-26 08:57:01', '2023-08-26 09:02:58', NULL, 0, 'FileM', '/fileM', '/fileM/file', 'Layout', '{"title": "文件管理", "svgIcon": "file", "alwaysShow": true}', 3),
(11, '2023-08-26 08:58:51', '2023-08-26 08:58:51', NULL, 10, 'File', '/fileM/file', '', 'fileM/file/index.vue', '{"title": "文件"}', 1),
(12, '2024-01-19 07:47:49', '2024-03-25 06:14:36', NULL, 0, 'Monitor', '/monitor', '/monitor/operationLog', 'Layout', '{"title": "系统监控", "svgIcon": "monitor", "alwaysShow": true}', 5),
(13, '2023-03-07 01:50:48', '2024-01-19 07:48:52', NULL, 12, 'OperationLog', 'operationLog', '', 'monitor/operationLog/index.vue', '{"title": "操作日志"}', 1),
(14, '2024-02-05 09:56:33', '2024-09-26 03:12:19', NULL, 0, 'SysTool', '/systool', '/systool/cron', 'Layout', '{"title": "系统工具", "hidden": true, "svgIcon": "config", "alwaysShow": true}', 4),
(15, '2024-02-06 10:00:00', '2024-02-06 10:00:00', NULL, 14, 'Cron', 'cron', '', 'sysTool/cron/index.vue', '{"title": "定时任务"}', 1),
(16, '2024-09-27 02:40:35', '2024-09-27 02:56:13', NULL, 0, 'Configuration', '/configuration', '/configuration/packing', 'Layout', '{"title": "基础配置管理", "svgIcon": "config", "alwaysShow": true}', 16),
(17, '2024-09-27 02:45:52', '2024-09-27 02:50:43', NULL, 16, 'Packing', 'packing', '', 'configuration/packing/index.vue', '{"title": "包装方式"}', 17),
(18, '2024-10-08 11:19:50', '2024-10-08 11:36:25', NULL, 16, 'Length', 'length', '', 'configuration/carLength/index.vue', '{"title": "车长管理"}', 18),
(19, '2024-10-08 11:32:10', '2024-10-08 11:36:36', NULL, 16, 'Model', 'model', '', 'configuration/carModel/index.vue', '{"title": "车型管理"}', 19);

SELECT setval('authority_menu_id_seq', 20);
COMMIT;

-- ----------------------------
-- Table structure for authority_role
-- ----------------------------
DROP TABLE IF EXISTS authority_role CASCADE;
CREATE TABLE authority_role (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  role_name VARCHAR(191) DEFAULT NULL,
  UNIQUE (role_name)
);

CREATE INDEX idx_sys_role_deleted_at ON authority_role (deleted_at);
CREATE INDEX idx_authority_role_deleted_at ON authority_role (deleted_at);

-- ----------------------------
-- Records of authority_role
-- ----------------------------
BEGIN;
INSERT INTO authority_role (id, created_at, updated_at, deleted_at, role_name) VALUES
(1, NULL, '2024-10-08 11:32:15', NULL, 'root');

SELECT setval('authority_role_id_seq', 2);
COMMIT;

-- ----------------------------
-- Table structure for authority_user
-- ----------------------------
DROP TABLE IF EXISTS authority_user CASCADE;
CREATE TABLE authority_user (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  username VARCHAR(191) DEFAULT NULL,
  password VARCHAR(191) NOT NULL,
  phone VARCHAR(191) DEFAULT NULL,
  email VARCHAR(191) DEFAULT NULL,
  active BOOLEAN DEFAULT NULL,
  role_model_id BIGINT NOT NULL,
  UNIQUE (username)
);

COMMENT ON COLUMN authority_user.username IS '用户名';
COMMENT ON COLUMN authority_user.password IS '密码';
COMMENT ON COLUMN authority_user.phone IS '手机号';
COMMENT ON COLUMN authority_user.email IS '邮箱';

CREATE INDEX idx_sys_user_deleted_at ON authority_user (deleted_at);
CREATE INDEX idx_sys_user_username ON authority_user (username);
CREATE INDEX idx_base_user_deleted_at ON authority_user (deleted_at);
CREATE INDEX idx_base_user_username ON authority_user (username);
CREATE INDEX idx_authority_user_deleted_at ON authority_user (deleted_at);
CREATE INDEX idx_authority_user_username ON authority_user (username);

-- ----------------------------
-- Records of authority_user
-- ----------------------------
BEGIN;
INSERT INTO authority_user (id, created_at, updated_at, deleted_at, username, password, phone, email, active, role_model_id) VALUES
(1, '2023-02-20 12:51:58', '2024-09-27 16:20:59', NULL, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '***********', '<EMAIL>', true, 1);

SELECT setval('authority_user_id_seq', 2);
COMMIT;

-- ----------------------------
-- Table structure for business_cargo
-- ----------------------------
DROP TABLE IF EXISTS business_cargo CASCADE;
CREATE TABLE business_cargo (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  packaging VARCHAR(100) DEFAULT NULL,
  weight_range VARCHAR(100) DEFAULT NULL,
  volume_range VARCHAR(100) DEFAULT NULL,
  temperature VARCHAR(50) DEFAULT NULL
);

COMMENT ON COLUMN business_cargo.id IS '货物ID';
COMMENT ON COLUMN business_cargo.name IS '货物名称';
COMMENT ON COLUMN business_cargo.packaging IS '包装方式';
COMMENT ON COLUMN business_cargo.weight_range IS '总重量区间';
COMMENT ON COLUMN business_cargo.volume_range IS '体积区间';
COMMENT ON COLUMN business_cargo.temperature IS '温控范围';

CREATE INDEX idx_business_cargo_deleted_at ON business_cargo (deleted_at);

-- ----------------------------
-- Table structure for business_cargo_copy1
-- ----------------------------
DROP TABLE IF EXISTS business_cargo_copy1 CASCADE;
CREATE TABLE business_cargo_copy1 (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  packaging VARCHAR(100) DEFAULT NULL,
  weight_range VARCHAR(100) DEFAULT NULL,
  volume_range VARCHAR(100) DEFAULT NULL,
  temperature VARCHAR(50) DEFAULT NULL
);

COMMENT ON COLUMN business_cargo_copy1.id IS '货物ID';
COMMENT ON COLUMN business_cargo_copy1.name IS '货物名称';
COMMENT ON COLUMN business_cargo_copy1.packaging IS '包装方式';
COMMENT ON COLUMN business_cargo_copy1.weight_range IS '总重量区间';
COMMENT ON COLUMN business_cargo_copy1.volume_range IS '体积区间';
COMMENT ON COLUMN business_cargo_copy1.temperature IS '温控范围';

CREATE INDEX idx_business_cargo_copy1_deleted_at ON business_cargo_copy1 (deleted_at);

-- ----------------------------
-- Table structure for business_loading_address
-- ----------------------------
DROP TABLE IF EXISTS business_loading_address CASCADE;
CREATE TABLE business_loading_address (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  address VARCHAR(255) DEFAULT NULL,
  contact_name VARCHAR(100) DEFAULT NULL,
  contact_phone VARCHAR(20) DEFAULT NULL
);

COMMENT ON COLUMN business_loading_address.id IS '装货地址ID';
COMMENT ON COLUMN business_loading_address.address IS '装货地址';
COMMENT ON COLUMN business_loading_address.contact_name IS '联系人姓名';
COMMENT ON COLUMN business_loading_address.contact_phone IS '联系人电话';

CREATE INDEX idx_business_loading_address_deleted_at ON business_loading_address (deleted_at);

-- ----------------------------
-- Table structure for business_unloading_address
-- ----------------------------
DROP TABLE IF EXISTS business_unloading_address CASCADE;
CREATE TABLE business_unloading_address (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  address VARCHAR(255) DEFAULT NULL,
  contact_name VARCHAR(100) DEFAULT NULL,
  contact_phone VARCHAR(20) DEFAULT NULL
);

COMMENT ON COLUMN business_unloading_address.id IS '卸货地址ID';
COMMENT ON COLUMN business_unloading_address.address IS '卸货地址';
COMMENT ON COLUMN business_unloading_address.contact_name IS '联系人姓名';
COMMENT ON COLUMN business_unloading_address.contact_phone IS '联系人电话';

CREATE INDEX idx_business_unloading_address_deleted_at ON business_unloading_address (deleted_at);

-- ----------------------------
-- Table structure for business_vehicle_requirement
-- ----------------------------
DROP TABLE IF EXISTS business_vehicle_requirement CASCADE;
CREATE TABLE business_vehicle_requirement (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  cargo_id BIGINT DEFAULT NULL,
  vehicle_count BIGINT DEFAULT NULL,
  vehicle_type VARCHAR(50) DEFAULT NULL,
  car_length VARCHAR(255) DEFAULT NULL,
  vehicle_model VARCHAR(255) DEFAULT NULL,
  loading_time VARCHAR(100) DEFAULT NULL,
  price_type VARCHAR(50) DEFAULT NULL,
  price_unit VARCHAR(50) DEFAULT NULL,
  notes TEXT
);

COMMENT ON COLUMN business_vehicle_requirement.id IS '需求ID';
COMMENT ON COLUMN business_vehicle_requirement.cargo_id IS '货物ID';
COMMENT ON COLUMN business_vehicle_requirement.vehicle_count IS '所需车辆数';
COMMENT ON COLUMN business_vehicle_requirement.vehicle_type IS '用车类型';
COMMENT ON COLUMN business_vehicle_requirement.car_length IS '车长';
COMMENT ON COLUMN business_vehicle_requirement.vehicle_model IS '车型';
COMMENT ON COLUMN business_vehicle_requirement.loading_time IS '装货时间';
COMMENT ON COLUMN business_vehicle_requirement.price_type IS '价格类型';
COMMENT ON COLUMN business_vehicle_requirement.price_unit IS '价格单位';
COMMENT ON COLUMN business_vehicle_requirement.notes IS '备注';

CREATE INDEX idx_business_vehicle_requirement_deleted_at ON business_vehicle_requirement (deleted_at);

-- ----------------------------
-- Table structure for business_vehicle_temperature_requirement
-- ----------------------------
DROP TABLE IF EXISTS business_vehicle_temperature_requirement CASCADE;
CREATE TABLE business_vehicle_temperature_requirement (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  requirement_id BIGINT DEFAULT NULL,
  temperature VARCHAR(50) DEFAULT NULL
);

COMMENT ON COLUMN business_vehicle_temperature_requirement.id IS '车辆温控要求ID';
COMMENT ON COLUMN business_vehicle_temperature_requirement.requirement_id IS '需求ID';
COMMENT ON COLUMN business_vehicle_temperature_requirement.temperature IS '温控要求';

CREATE INDEX idx_business_vehicle_temperature_requirement_deleted_at ON business_vehicle_temperature_requirement (deleted_at);

-- ----------------------------
-- Table structure for car_length
-- ----------------------------
DROP TABLE IF EXISTS car_length CASCADE;
CREATE TABLE car_length (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  min_length DOUBLE PRECISION NOT NULL,
  max_length DOUBLE PRECISION NOT NULL,
  min_width DOUBLE PRECISION NOT NULL,
  max_width DOUBLE PRECISION NOT NULL,
  min_height DOUBLE PRECISION NOT NULL,
  max_height DOUBLE PRECISION NOT NULL,
  min_load DOUBLE PRECISION NOT NULL,
  max_load DOUBLE PRECISION NOT NULL,
  min_volume DOUBLE PRECISION NOT NULL,
  max_volume DOUBLE PRECISION NOT NULL,
  image VARCHAR(191) DEFAULT NULL
);

COMMENT ON COLUMN car_length.min_length IS '最小长度';
COMMENT ON COLUMN car_length.max_length IS '最大长度';
COMMENT ON COLUMN car_length.min_width IS '最小宽度';
COMMENT ON COLUMN car_length.max_width IS '最大宽度';
COMMENT ON COLUMN car_length.min_height IS '最小高度';
COMMENT ON COLUMN car_length.max_height IS '最大高度';
COMMENT ON COLUMN car_length.min_load IS '最小载重';
COMMENT ON COLUMN car_length.max_load IS '最大载重';
COMMENT ON COLUMN car_length.min_volume IS '最小体积';
COMMENT ON COLUMN car_length.max_volume IS '最大体积';
COMMENT ON COLUMN car_length.image IS '车型图片';

CREATE INDEX idx_car_length_deleted_at ON car_length (deleted_at);

-- ----------------------------
-- Records of car_length
-- ----------------------------
BEGIN;
INSERT INTO car_length (id, created_at, updated_at, deleted_at, min_length, max_length, min_width, max_width, min_height, max_height, min_load, max_load, min_volume, max_volume, image) VALUES
(8, '2024-11-22 05:59:36', '2024-11-22 05:59:36', NULL, 1, 2, 1, 2, 1, 1, 0, 1, 2, 4, ''),
(9, '2024-11-22 06:35:24', '2024-11-22 06:35:24', NULL, 2, 3, 1, 2, 1, 2, 1, 1, 4, 6, ''),
(10, '2024-11-22 06:36:50', '2024-11-22 06:36:50', NULL, 3, 3, 2, 2, 2, 2, 1, 2, 6, 11, ''),
(11, '2024-11-22 06:38:05', '2024-11-22 06:38:05', NULL, 3, 4, 2, 2, 2, 2, 1, 2, 7, 12, ''),
(12, '2024-11-22 06:39:17', '2024-11-22 06:39:17', NULL, 4, 4, 2, 2, 2, 2, 2, 2, 10, 22, ''),
(13, '2024-11-22 06:40:43', '2024-11-22 06:40:43', NULL, 5, 5, 2, 2, 2, 2, 4, 6, 17, 26, ''),
(14, '2024-11-22 06:41:41', '2024-11-22 06:41:41', NULL, 6, 6, 2, 2, 2, 2, 6, 10, 20, 33, ''),
(15, '2024-11-22 06:42:44', '2024-11-22 06:42:44', NULL, 6, 7, 2, 3, 2, 3, 6, 10, 35, 50, ''),
(16, '2024-11-22 06:44:04', '2024-11-22 06:44:04', NULL, 7, 8, 2, 3, 2, 3, 8, 12, 40, 56, ''),
(17, '2024-11-22 06:45:19', '2024-11-22 06:45:19', NULL, 8, 8, 2, 3, 2, 3, 8, 12, 42, 60, ''),
(18, '2024-11-22 06:46:39', '2024-11-22 06:46:39', NULL, 8, 9, 2, 3, 2, 3, 8, 12, 45, 63, ''),
(19, '2024-11-22 06:47:45', '2024-11-22 06:47:45', NULL, 9, 10, 2, 3, 2, 3, 10, 18, 50, 70, ''),
(20, '2024-11-22 06:48:57', '2024-11-22 06:48:57', NULL, 11, 12, 2, 3, 2, 3, 18, 32, 64, 91, ''),
(21, '2024-11-22 06:50:19', '2024-11-22 06:50:19', NULL, 12, 12, 2, 3, 2, 3, 18, 32, 69, 98, ''),
(22, '2024-11-22 06:51:14', '2024-11-22 06:51:14', NULL, 12, 13, 2, 3, 2, 3, 18, 32, 72, 101, ''),
(23, '2024-11-22 06:52:17', '2024-11-22 06:52:17', NULL, 13, 14, 2, 3, 2, 3, 25, 35, 76, 123, ''),
(24, '2024-11-22 06:54:08', '2024-11-22 06:54:08', NULL, 14, 15, 2, 3, 2, 3, 25, 35, 83, 135, ''),
(25, '2024-11-22 06:55:09', '2024-11-22 06:55:09', NULL, 16, 16, 2, 3, 2, 3, 25, 35, 89, 144, ''),
(26, '2024-11-22 06:56:24', '2024-11-22 06:56:24', NULL, 17, 18, 2, 3, 2, 3, 25, 35, 98, 158, '');

SELECT setval('car_length_id_seq', 28);
COMMIT;

-- ----------------------------
-- Table structure for car_model
-- ----------------------------
DROP TABLE IF EXISTS car_model CASCADE;
CREATE TABLE car_model (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  name VARCHAR(191) NOT NULL,
  description VARCHAR(191) NOT NULL,
  enable BOOLEAN DEFAULT true
);

COMMENT ON COLUMN car_model.name IS '车型名称';
COMMENT ON COLUMN car_model.description IS '车型描述';
COMMENT ON COLUMN car_model.enable IS '是否启用';

CREATE INDEX idx_car_model_deleted_at ON car_model (deleted_at);

-- ----------------------------
-- Records of car_model
-- ----------------------------
BEGIN;
INSERT INTO car_model (id, created_at, updated_at, deleted_at, name, description, enable) VALUES
(6, '2024-11-22 05:55:03', '2024-11-22 05:55:03', NULL, '平板', '平板', true),
(7, '2024-11-22 05:55:11', '2024-11-22 05:55:11', NULL, '高栏', '高栏', true),
(8, '2024-11-22 05:55:34', '2024-11-22 05:55:34', NULL, '厢式', '厢式', true),
(9, '2024-11-22 05:55:46', '2024-11-22 05:55:46', NULL, '集装箱', '集装箱', true),
(10, '2024-11-22 05:56:02', '2024-11-22 05:56:02', NULL, '冷藏', '冷藏', true),
(11, '2024-11-22 05:56:13', '2024-11-22 05:56:13', NULL, '保温', '保温', true),
(12, '2024-11-22 05:56:27', '2024-11-22 05:56:27', NULL, '高低板', '高低板', true),
(13, '2024-11-22 05:56:46', '2024-11-22 05:56:46', NULL, '面包车', '面包车', true),
(14, '2024-11-22 05:56:56', '2024-11-22 05:56:56', NULL, '棉被车', '棉被车', true),
(15, '2024-11-22 05:57:04', '2024-11-22 05:57:04', NULL, '爬梯车', '爬梯车', true),
(16, '2024-11-22 05:57:21', '2024-11-22 05:57:21', NULL, '飞翼车', '飞翼车', true),
(17, '2024-11-22 05:57:30', '2024-11-22 05:57:30', NULL, '依维柯', '依维柯', true);

SELECT setval('car_model_id_seq', 19);
COMMIT;

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS casbin_rule CASCADE;
CREATE TABLE casbin_rule (
  id BIGSERIAL PRIMARY KEY,
  ptype VARCHAR(100) DEFAULT NULL,
  v0 VARCHAR(100) DEFAULT NULL,
  v1 VARCHAR(100) DEFAULT NULL,
  v2 VARCHAR(100) DEFAULT NULL,
  v3 VARCHAR(100) DEFAULT NULL,
  v4 VARCHAR(100) DEFAULT NULL,
  v5 VARCHAR(100) DEFAULT NULL,
  UNIQUE (ptype, v0, v1, v2, v3, v4, v5)
);

-- ----------------------------
-- Records of casbin_rule
-- ----------------------------
BEGIN;
INSERT INTO casbin_rule (id, ptype, v0, v1, v2, v3, v4, v5) VALUES
(1045, 'p', '1', '/api/addApi', 'POST', '', '', ''),
(1047, 'p', '1', '/api/deleteApi', 'POST', '', '', ''),
(1050, 'p', '1', '/api/deleteApiById', 'POST', '', '', ''),
(1048, 'p', '1', '/api/editApi', 'POST', '', '', ''),
(1046, 'p', '1', '/api/getApis', 'POST', '', '', ''),
(1049, 'p', '1', '/api/getElTreeApis', 'POST', '', '', ''),
(1076, 'p', '1', '/carLength/addCarLength', 'POST', '', '', ''),
(1075, 'p', '1', '/carLength/carLengthList', 'GET', '', '', ''),
(1078, 'p', '1', '/carLength/deleteCarLength', 'DELETE', '', '', ''),
(1077, 'p', '1', '/carLength/editCarLength', 'PUT', '', '', ''),
(1027, 'p', '1', '/casbin/editCasbin', 'POST', '', '', ''),
(1059, 'p', '1', '/cron/addCron', 'POST', '', '', ''),
(1060, 'p', '1', '/cron/deleteCron', 'POST', '', '', ''),
(1061, 'p', '1', '/cron/editCron', 'POST', '', '', ''),
(1058, 'p', '1', '/cron/getCronList', 'POST', '', '', ''),
(1062, 'p', '1', '/cron/switchOpen', 'POST', '', '', ''),
(1057, 'p', '1', '/file/delete', 'GET', '', '', ''),
(1056, 'p', '1', '/file/download', 'GET', '', '', ''),
(1055, 'p', '1', '/file/getFileList', 'POST', '', '', ''),
(1054, 'p', '1', '/file/upload', 'POST', '', '', ''),
(1024, 'p', '1', '/logReg/captcha', 'POST', '', '', ''),
(1025, 'p', '1', '/logReg/login', 'POST', '', '', ''),
(1026, 'p', '1', '/logReg/logout', 'POST', '', '', ''),
(1041, 'p', '1', '/menu/addMenu', 'POST', '', '', ''),
(1043, 'p', '1', '/menu/deleteMenu', 'POST', '', '', ''),
(1042, 'p', '1', '/menu/editMenu', 'POST', '', '', ''),
(1044, 'p', '1', '/menu/getElTreeMenus', 'POST', '', '', ''),
(1040, 'p', '1', '/menu/getMenus', 'GET', '', '', ''),
(1070, 'p', '1', '/model/addModel', 'POST', '', '', ''),
(1071, 'p', '1', '/model/delModel', 'DELETE', '', '', ''),
(1073, 'p', '1', '/model/editModel', 'PUT', '', '', ''),
(1069, 'p', '1', '/model/getModel', 'GET', '', '', ''),
(1074, 'p', '1', '/model/switchEnable', 'PUT', '', '', ''),
(1052, 'p', '1', '/opl/deleteOpl', 'POST', '', '', ''),
(1053, 'p', '1', '/opl/deleteOplByIds', 'POST', '', '', ''),
(1051, 'p', '1', '/opl/getOplList', 'POST', '', '', ''),
(1063, 'p', '1', '/packing/addPacking', 'POST', '', '', ''),
(1068, 'p', '1', '/packing/batchDelPacking', 'DELETE', '', '', ''),
(1067, 'p', '1', '/packing/delPacking', 'DELETE', '', '', ''),
(1065, 'p', '1', '/packing/editPacking', 'PUT', '', '', ''),
(1064, 'p', '1', '/packing/getPacking', 'GET', '', '', ''),
(1072, 'p', '1', '/packing/getTrunkType', 'GET', '', '', ''),
(1066, 'p', '1', '/packing/switchEnable', 'PUT', '', '', ''),
(1036, 'p', '1', '/role/addRole', 'POST', '', '', ''),
(1037, 'p', '1', '/role/deleteRole', 'POST', '', '', ''),
(1038, 'p', '1', '/role/editRole', 'POST', '', '', ''),
(1039, 'p', '1', '/role/editRoleMenu', 'POST', '', '', ''),
(1035, 'p', '1', '/role/getRoles', 'POST', '', '', ''),
(1031, 'p', '1', '/user/addUser', 'POST', '', '', ''),
(1030, 'p', '1', '/user/deleteUser', 'POST', '', '', ''),
(1032, 'p', '1', '/user/editUser', 'POST', '', '', ''),
(1028, 'p', '1', '/user/getUserInfo', 'GET', '', '', ''),
(1029, 'p', '1', '/user/getUsers', 'POST', '', '', ''),
(1033, 'p', '1', '/user/modifyPass', 'POST', '', '', ''),
(1034, 'p', '1', '/user/switchActive', 'POST', '', '', '');

SELECT setval('casbin_rule_id_seq', 1079);
COMMIT;

-- ----------------------------
-- Table structure for client_user
-- ----------------------------
DROP TABLE IF EXISTS client_user CASCADE;
CREATE TABLE client_user (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  phone VARCHAR(191) NOT NULL,
  password VARCHAR(191) NOT NULL,
  picture VARCHAR(191) DEFAULT NULL,
  enable BOOLEAN DEFAULT true,
  source BIGINT DEFAULT 1
);

COMMENT ON COLUMN client_user.phone IS '手机号';
COMMENT ON COLUMN client_user.password IS '密码';
COMMENT ON COLUMN client_user.picture IS '头像';
COMMENT ON COLUMN client_user.enable IS '是否禁用';
COMMENT ON COLUMN client_user.source IS '来源';

CREATE INDEX idx_client_user_deleted_at ON client_user (deleted_at);

-- ----------------------------
-- Records of client_user
-- ----------------------------
BEGIN;
INSERT INTO client_user (id, created_at, updated_at, deleted_at, phone, password, picture, enable, source) VALUES
(1, '2024-11-21 06:55:11', '2024-11-21 06:55:11', NULL, '18231319481', 'd41d8cd98f00b204e9800998ecf8427e', '', true, 1);

SELECT setval('client_user_id_seq', 2);
COMMIT;

-- ----------------------------
-- Table structure for configuration_packing
-- ----------------------------
DROP TABLE IF EXISTS configuration_packing CASCADE;
CREATE TABLE configuration_packing (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  type_name VARCHAR(191) NOT NULL,
  description VARCHAR(191) NOT NULL,
  enable BOOLEAN DEFAULT true
);

COMMENT ON COLUMN configuration_packing.type_name IS '包装方式名称';
COMMENT ON COLUMN configuration_packing.description IS '包装方式描述';
COMMENT ON COLUMN configuration_packing.enable IS '是否启用';

CREATE INDEX idx_configuration_packing_deleted_at ON configuration_packing (deleted_at);

-- ----------------------------
-- Records of configuration_packing
-- ----------------------------
BEGIN;
INSERT INTO configuration_packing (id, created_at, updated_at, deleted_at, type_name, description, enable) VALUES
(10, '2024-11-25 03:29:10', '2024-11-25 03:29:10', NULL, '其他', '其他', true),
(11, '2024-11-25 03:29:23', '2024-11-25 03:29:23', NULL, '架子', '架子', true),
(12, '2024-11-25 03:29:31', '2024-11-25 03:29:31', NULL, '木箱', '木箱', true),
(13, '2024-11-25 03:29:39', '2024-11-25 03:29:39', NULL, '散装', '散装', true),
(14, '2024-11-25 03:29:50', '2024-11-25 03:29:50', NULL, '捆扎', '捆扎', true),
(15, '2024-11-25 03:29:56', '2024-11-25 03:29:56', NULL, '吨包', '吨包', true);

SELECT setval('configuration_packing_id_seq', 16);
COMMIT;

-- ----------------------------
-- Table structure for fileM_file
-- ----------------------------
DROP TABLE IF EXISTS fileM_file CASCADE;
CREATE TABLE fileM_file (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  file_name VARCHAR(191) DEFAULT NULL,
  full_path VARCHAR(191) DEFAULT NULL,
  mime VARCHAR(191) DEFAULT NULL
);

COMMENT ON COLUMN fileM_file.file_name IS '文件名';
COMMENT ON COLUMN fileM_file.full_path IS '文件完整路径';
COMMENT ON COLUMN fileM_file.mime IS '文件类型';

CREATE INDEX idx_fileM_file_deleted_at ON fileM_file (deleted_at);

-- ----------------------------
-- Table structure for model_length
-- ----------------------------
DROP TABLE IF EXISTS model_length CASCADE;
CREATE TABLE model_length (
  length_modal_id BIGINT NOT NULL,
  car_type_modal_id BIGINT NOT NULL,
  PRIMARY KEY (length_modal_id, car_type_modal_id)
);

-- ----------------------------
-- Records of model_length (sample data - full data available in original file)
-- ----------------------------
BEGIN;
INSERT INTO model_length (length_modal_id, car_type_modal_id) VALUES
(8, 6), (8, 7), (8, 11), (8, 12), (8, 14), (8, 15),
(9, 6), (9, 7), (9, 8), (9, 10), (9, 11), (9, 12), (9, 14), (9, 15), (9, 18),
(10, 6), (10, 7), (10, 8), (10, 10), (10, 11), (10, 12), (10, 14), (10, 15), (10, 18),
(11, 6), (11, 7), (11, 8), (11, 10), (11, 11), (11, 12), (11, 14), (11, 15), (11, 18),
(12, 6), (12, 7), (12, 8), (12, 10), (12, 11), (12, 12), (12, 14), (12, 15), (12, 17),
(13, 6), (13, 7), (13, 8), (13, 10), (13, 11), (13, 12), (13, 14), (13, 15), (13, 17), (13, 18),
(14, 6), (14, 7), (14, 8), (14, 9), (14, 10), (14, 11), (14, 12), (14, 15), (14, 17),
(15, 6), (15, 7), (15, 8), (15, 9), (15, 10), (15, 11), (15, 12), (15, 15), (15, 16), (15, 17),
(16, 6), (16, 7), (16, 8), (16, 9), (16, 10), (16, 11), (16, 12), (16, 15), (16, 16), (16, 17),
(17, 6), (17, 7), (17, 8), (17, 9), (17, 10), (17, 11), (17, 12), (17, 15), (17, 16), (17, 17),
(18, 6), (18, 7), (18, 8), (18, 9), (18, 10), (18, 11), (18, 12), (18, 15), (18, 16), (18, 17),
(19, 6), (19, 7), (19, 8), (19, 9), (19, 10), (19, 11), (19, 12), (19, 15), (19, 16), (19, 17),
(20, 6), (20, 7), (20, 8), (20, 9), (20, 11), (20, 12), (20, 15), (20, 16), (20, 17),
(21, 6), (21, 7), (21, 8), (21, 9), (21, 11), (21, 12), (21, 15), (21, 16), (21, 17),
(22, 6), (22, 7), (22, 8), (22, 9), (22, 10), (22, 11), (22, 12), (22, 13), (22, 15), (22, 16), (22, 17),
(23, 6), (23, 8), (23, 9), (23, 11), (23, 12), (23, 13), (23, 15), (23, 16), (23, 17),
(24, 6), (24, 7), (24, 8), (24, 9), (24, 11), (24, 12), (24, 13), (24, 15), (24, 16), (24, 17),
(25, 6), (25, 7), (25, 8), (25, 9), (25, 11), (25, 12), (25, 13), (25, 15), (25, 16), (25, 17),
(26, 6), (26, 7), (26, 8), (26, 9), (26, 11), (26, 12), (26, 13), (26, 15), (26, 16), (26, 17);
COMMIT;

-- ----------------------------
-- Table structure for monitor_operationLog
-- ----------------------------
DROP TABLE IF EXISTS monitor_operationLog CASCADE;
CREATE TABLE monitor_operationLog (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL,
  ip VARCHAR(191) DEFAULT NULL,
  method VARCHAR(191) DEFAULT NULL,
  path VARCHAR(191) DEFAULT NULL,
  status BIGINT DEFAULT NULL,
  user_agent VARCHAR(191) DEFAULT NULL,
  req_param TEXT,
  resp_data TEXT,
  resp_time BIGINT DEFAULT NULL,
  user_id BIGINT DEFAULT NULL,
  user_name VARCHAR(191) DEFAULT NULL
);

COMMENT ON COLUMN monitor_operationLog.ip IS '请求ip';
COMMENT ON COLUMN monitor_operationLog.method IS '请求方法';
COMMENT ON COLUMN monitor_operationLog.path IS '请求路径';
COMMENT ON COLUMN monitor_operationLog.status IS '请求状态';
COMMENT ON COLUMN monitor_operationLog.req_param IS '请求Body';
COMMENT ON COLUMN monitor_operationLog.resp_data IS '响应数据';
COMMENT ON COLUMN monitor_operationLog.user_id IS '用户id';
COMMENT ON COLUMN monitor_operationLog.user_name IS '用户名称';

CREATE INDEX idx_monitor_operationLog_deleted_at ON monitor_operationLog (deleted_at);

-- Note: Operation log records are not included in this conversion as they are typically
-- runtime data that would be generated fresh in the new PostgreSQL environment.

-- ----------------------------
-- Final Notes
-- ----------------------------
-- This PostgreSQL conversion includes:
-- 1. All table structures converted from MySQL to PostgreSQL syntax
-- 2. Data type conversions (bigint unsigned -> BIGSERIAL/BIGINT, tinyint(1) -> BOOLEAN, etc.)
-- 3. Index conversions
-- 4. Sample data for all configuration and master data tables
-- 5. Proper sequence management for auto-increment fields
-- 6. JSONB usage for JSON fields for better performance
-- 7. Removed MySQL-specific syntax (ENGINE, CHARSET, backticks, etc.)
--
-- To use this file:
-- 1. Create a PostgreSQL database
-- 2. Run this script to create all tables and insert initial data
-- 3. Verify all sequences are properly set
-- 4. Test application connectivity and functionality

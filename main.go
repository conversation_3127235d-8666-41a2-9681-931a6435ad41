package main

import (
	"go.uber.org/zap"
	"os"

	"server/core"
	"server/global"
	"server/initialize"
)

// @title 接囗文档
// @version 1.0
// @description 货运项目
// @termsofservice https://www.victoryblog.top/
// @contact.name wangxiumin
// @contact.email <EMAIL>
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        x-token
func main() {
	global.FreightVp = core.Viper() // 初始化viper
	global.FreightLog = core.Zap()  // 初始化zap日志
	zap.ReplaceGlobals(global.FreightLog)
	global.FreightDb = initialize.Gorm()       // gorm连接数据库
	initialize.Redis()                         // 初始化redis
	global.FreightCron = initialize.InitCron() // 初始化cron
	initialize.CheckCron()                     // start cron entry, if exists
	if global.FreightDb == nil {
		global.FreightLog.Error("postgresql连接失败，退出程序")
		os.Exit(127)
	} else {
		initialize.RegisterTables(global.FreightDb) // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.FreightDb.DB()
		defer db.Close()
	}
	core.RunServer()
}
